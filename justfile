set shell := ["bash", "-cu"]

default:
    @just --list --unsorted

alias b := baml-generate
alias cb := cleanup-branch
alias thread := get-thread-human-messages
alias trip := get-thread-human-messages

_validate-env env:
    if [[ "{{env}}" != "DEV" && "{{env}}" != "STG" && "{{env}}" != "LIVE" ]]; then \
        echo "Error: env must be one of DEV, STG, or LIVE" >&2 && exit 1; \
    fi

cleanup-branch-dry-run:
    git branch -r --merged main | grep -v 'main$' | grep -v 'staging$' | grep -v 'development$' | sed 's/origin\///'

cleanup-branch:
    git branch -r --merged main | grep -v 'main$' | grep -v 'staging$' | grep -v 'development$' | sed 's/origin\///' | xargs -n 1 git push origin --delete

baml-generate:
    uv run baml-cli generate

install-uv:
    curl -LsSf https://astral.sh/uv/install.sh | sh

dev-setup:
    uv sync
    uv run pre-commit install

dev-start: baml-generate
    if [[ "$(uname)" == "Darwin" ]]; then \
        export DYLD_LIBRARY_PATH="/opt/homebrew/lib:${DYLD_LIBRARY_PATH:-}"; \
        export PATH="/opt/homebrew/bin:$PATH"; \
    fi; \
    uv run uvicorn server.main:app --reload --port 8000 --host 0.0.0.0 --ssl-certfile certificates/api.local.dev.otto-demo.com.pem --ssl-keyfile certificates/api.local.dev.otto-demo.com-key.pem

generate-otc group count="1" env="DEV":
    PYTHONPATH=. OTTO_ENV={{env}} uv run python scripts/generate_otc_invite.py --group {{group}} --count {{count}}

get-spotnana-token env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run python scripts/get_spotnana_token.py

delete-thread env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run python scripts/delete_thread.py

alembic-upgrade-dryrun:
    source .env && \
    echo "Running dry run on environment: ${OTTO_ENV}" && \
    if uv run alembic current | grep -q "(head)"; then \
        echo "Already at head. Nothing to upgrade."; \
    else \
        current_version=$(uv run alembic current | awk '/[a-f0-9]+/{print $1}') && \
        uv run alembic upgrade "$current_version:head" --sql; \
    fi

alembic-upgrade: alembic-upgrade-dryrun
    @read -p "Confirm if you checked above dryrun output and would like to perform the upgrade? (y/N) " confirm && \
    [[ "$confirm" == "Y" || "$confirm" == "y" ]] && \
    uv run alembic upgrade head || \
    echo "Upgrade aborted."

alembic-downgrade-previous:
    uv run alembic downgrade -1

mark-admin env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run scripts/mark_users_as_admin.py

delete-user env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run scripts/delete_user.py

delete-empty-threads days="7" env="DEV" dry_run="false":
    @just _validate-env {{env}}
    if [[ "{{dry_run}}" == "true" ]]; then \
        PYTHONPATH=. OTTO_ENV={{env}} uv run python scripts/delete_empty_threads.py --days {{days}} --dry-run; \
    else \
        PYTHONPATH=. OTTO_ENV={{env}} uv run python scripts/delete_empty_threads.py --days {{days}}; \
    fi

get-thread-human-messages thread_id env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run python -m scripts.get_thread_human_messages {{thread_id}}

create-trip user_id env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run python -m scripts.create_trip_and_connect {{user_id}}

extract-ssr-categories env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run python -m scripts.extract_ssr_categories

create-organization env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run scripts/create_organization.py

cleanup-company-policy env="DEV":
    @just _validate-env {{env}}
    PYTHONPATH=. OTTO_ENV={{env}} uv run scripts/cleanup_company_policy.py
