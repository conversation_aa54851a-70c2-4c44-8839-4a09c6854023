import asyncio
import json
from typing import Any, Callable, Coroutine, Literal, Optional

from langchain_core.messages import AIMessage, BaseMessage

import front_of_house_agent.back_of_house_executor.flight_and_hotel_executor as fhe
from baml_client import b
from baml_client.tracing import trace
from baml_client.types import (
    FlightPlanningStep,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightType,
)
from flight_agent.flights_tools import FlightSearchTools
from front_of_house_agent import flight_utils
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.common_models import (
    FlightSearchParams,
    FlightSearchSource,
    FlightSearchType,
    TravelContext,
)
from front_of_house_agent.flight_utils import (
    get_flight_search_id_for_return_search_from_serp_flight_id,
    track_fallback_spotnana_metrics,
    track_serp_not_found_metrics,
)
from front_of_house_agent.serp_common_models import Booking
from llm_utils.llm_utils import (
    get_flight_detail_from_history,
    is_valid_future_date,
    reconcile_llm_inferred_airline_iata_code,
)
from server.services.google_maps_api.get_route_duration import get_route_duration
from server.utils.logger import logger
from server.utils.message_constants import FLIGHT_SKELETON_MESSAGES
from server.utils.mongo_connector import preferred_airline_per_airport_collection
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.common_models import AgentError
from virtual_travel_agent.helpers import console_masks, get_current_date_string

flight_mask = console_masks["flight"]


async def _generate_duration_from_airport_to_destination(airports: list[str], messages: list[str]) -> Optional[str]:
    destination_city = await b.GetDestinationLocation(messages=messages)

    tasks = []
    for airport in airports:
        tasks.append(
            get_route_duration(
                origin_airport=airport,
                destination=destination_city,
                travel_mode="DRIVE",
            )
        )
    durations = await asyncio.gather(*tasks)
    res = []
    for airport, duration_seconds in zip(airports, durations):
        if duration_seconds is not None:
            minutes = round(duration_seconds / 60)
            mins = f"{minutes} minute{'s' if minutes != 1 else ''}"
            res.append((airport, mins))
    if res:
        res.sort(key=lambda x: x[1])
        return ", ".join([f"{airport} to {destination_city} is approximately {mins}" for airport, mins in res])
    return None


def __determine_search_source_for_outbound_search(
    current_segment_index: int | None,
    existing_flight_search_source: FlightSearchSource | None,
    flight_search_criteria_additional: FlightSearchAdditionalCriteria,
):
    if settings.SPOTNANA_FLIGHT_BY_DEFAULT:
        return FlightSearchSource.FALLBACK_SPOTNANA
    if current_segment_index is not None:
        return FlightSearchSource.FALLBACK_SPOTNANA
    if flight_search_criteria_additional.refundable or flight_search_criteria_additional.upgrade:
        return FlightSearchSource.FALLBACK_SPOTNANA
    if existing_flight_search_source == FlightSearchSource.FALLBACK_SPOTNANA:
        return FlightSearchSource.FALLBACK_SPOTNANA
    return FlightSearchSource.SERP


async def _get_duration_suggestion(
    flight_search_criteria: FlightSearchParams, message_buffer_str: list[str]
) -> Optional[str]:
    if (
        flight_search_criteria["is_arrival_iata_city_code"]
        and len(settings.METRO_AREA_CODES.get(flight_search_criteria["arrival_airport_code"] or "") or []) > 1
    ):
        # If the user searched by IATA city code, we can suggest a duration based on the metro area
        airport_codes = settings.METRO_AREA_CODES.get(flight_search_criteria["arrival_airport_code"] or "")
        return await _generate_duration_from_airport_to_destination(
            airport_codes or [],
            message_buffer_str,
        )
    return None


@trace
async def search_flights(
    fh_executor: "fhe.TripPlanExecutor",
    step_name: Literal["OUTBOUND_FLIGHT_SEARCH", "FLIGHT_SEARCH", "RETURN_FLIGHT_SEARCH"],
    current_working_segment_index: int | None,  # only for multi-leg search
    travel_context: TravelContext,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    extral_params: dict[str, Any],
    callback_handlers: dict[str, Callable],
) -> BaseMessage:
    assert step_name in [
        FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name,
        FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name,
        FlightPlanningStep.RETURN_FLIGHT_SEARCH.name,
    ]

    # Determine which airport to use for airline preferences
    airport_code_for_preferences = (
        travel_context.flight_search_core_criteria.arrival_airport_code
        if step_name == FlightPlanningStep.RETURN_FLIGHT_SEARCH.name
        else travel_context.flight_search_core_criteria.departure_airport_code
    )

    # Get reconciled preferred airline codes
    reconciled_preferred_airline_codes = None
    if travel_context.flight_search_additional_criteria.preferred_airline_codes:
        reconciled_preferred_airline_codes = [
            reconciled
            for airline_code in travel_context.flight_search_additional_criteria.preferred_airline_codes
            if (reconciled := reconcile_llm_inferred_airline_iata_code(airline_code)) is not None
        ]
        logger.info(
            f"Reconciled preferred airline codes: {reconciled_preferred_airline_codes} from {travel_context.flight_search_additional_criteria.preferred_airline_codes}"
        )

    # Get default airline brands only if no preferred airlines
    default_airline_brands_from_airport_code = None
    if not reconciled_preferred_airline_codes:
        default_airline_dict = await preferred_airline_per_airport_collection.find_one(
            {"IATA_CODE": airport_code_for_preferences}
        )
        if default_airline_dict:
            default_airline_brands_from_airport_code = default_airline_dict.get("AIRLINE_CODES")

    # Determine selected outbound flight ID based on step type
    selected_outbound_flight_id = travel_context.flight_select_result.selected_outbound_flight_id
    search_id = travel_context.flight_select_result.search_id

    if step_name in [FlightPlanningStep.RETURN_FLIGHT_SEARCH.name, FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name]:
        selected_outbound_flight_id = selected_outbound_flight_id or (
            next(
                (
                    x.selected_flight_id
                    for x in (travel_context.flight_select_result.selected_flight_for_segment or [])
                    if x.segment_index
                    == (current_working_segment_index - 1 if current_working_segment_index is not None else -1)
                ),
                None,
            )
        )
        search_id = None
        if selected_outbound_flight_id:
            candidate = get_flight_detail_from_history(extral_params.get("messages") or [], selected_outbound_flight_id)
            if candidate:
                search_id = candidate[1]

    # Determine if default airline brands should be included
    is_us_domestic = FlightSearchTools.is_us_domestic_flight(
        travel_context.flight_search_core_criteria.departure_airport_code,
        travel_context.flight_search_core_criteria.arrival_airport_code,
        travel_context.flight_search_core_criteria.is_departure_iata_city_code,
        travel_context.flight_search_core_criteria.is_arrival_iata_city_code,
    )
    is_premium_cabin_search = flight_utils.is_premium_cabin(travel_context.flight_search_additional_criteria.cabin)
    include_default_airline_brands = (
        is_us_domestic
        and not is_premium_cabin_search
        and not travel_context.flight_search_additional_criteria.ignore_all_airlines
    )

    # Build travel context
    travel_context_dict = travel_context.flight_search_additional_criteria.model_dump(exclude_none=True)
    travel_context_dict["refundable"] = (
        "not_specified"
        if travel_context.flight_search_additional_criteria.refundable is None
        else str(travel_context.flight_search_additional_criteria.refundable)
    )

    # Build flight search parameters
    current_step = (
        "OUTBOUND_FLIGHT_SEARCH"
        if step_name != FlightPlanningStep.RETURN_FLIGHT_SEARCH.name
        else "RETURN_FLIGHT_SEARCH"
    )

    flight_search_criteria: FlightSearchParams = {
        "current_step": current_step,
        "departure_airport_code": travel_context.flight_search_core_criteria.departure_airport_code,
        "is_departure_iata_city_code": travel_context.flight_search_core_criteria.is_departure_iata_city_code,
        "arrival_airport_code": travel_context.flight_search_core_criteria.arrival_airport_code,
        "is_arrival_iata_city_code": travel_context.flight_search_core_criteria.is_arrival_iata_city_code,
        "outbound_date": travel_context.flight_search_core_criteria.outbound_date,
        "return_date": None
        if travel_context.flight_search_core_criteria.flight_type == FlightType.OneWay
        else travel_context.flight_search_core_criteria.return_date,
        "flight_type": travel_context.flight_search_core_criteria.flight_type,
        "preferred_airline_codes": reconciled_preferred_airline_codes,
        "default_airline_brands": default_airline_brands_from_airport_code if include_default_airline_brands else None,
        "preferred_cabin": travel_context.flight_search_additional_criteria.cabin,
        "travel_context": json.dumps(travel_context_dict),
        "number_of_stops": travel_context.flight_search_additional_criteria.number_of_stops,
        "search_id": search_id,
        "selected_outbound_flight_id": selected_outbound_flight_id,
        "outbound_arrival_time": travel_context.flight_search_additional_criteria.outbound_arrival_time,
        "outbound_departure_time": travel_context.flight_search_additional_criteria.outbound_departure_time,
        "return_arrival_time": travel_context.flight_search_additional_criteria.return_arrival_time,
        "return_departure_time": travel_context.flight_search_additional_criteria.return_departure_time,
        "search_segments": travel_context.flight_search_core_criteria.flight_segments,
    }

    # Determine if this is an outbound search
    is_outbound_search = (
        step_name == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name
        or step_name == FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name
    )

    validation_message = await validate_flight_search_params(
        fh_executor, FlightSearchCoreCriteria.model_validate(flight_search_criteria)
    )
    if validation_message:
        await message_persistor([validation_message])
        message = await map_websocket_message(
            validation_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})
        return validation_message
    assert flight_search_criteria["departure_airport_code"], "departure_airport_code is required"
    assert flight_search_criteria["arrival_airport_code"], "arrival_airport_code is required"
    flight_search_type = (
        FlightSearchType.ONE_WAY
        if travel_context.flight_search_core_criteria.flight_type == FlightType.OneWay
        else FlightSearchType.ROUND_TRIP
        if travel_context.flight_search_core_criteria.flight_type == FlightType.RoundTrip
        else FlightSearchType.MULTI_CITY
    )
    # Give user a more accurate estimate of the search time based on the flight type
    search_estimation_seconds = 10 if flight_search_type == FlightSearchType.ONE_WAY else 30
    if flight_search_type == FlightSearchType.MULTI_CITY:
        initial_search_message = FLIGHT_SKELETON_MESSAGES["MULTI_CITY_SEARCH"]
    else:
        initial_search_message = FLIGHT_SKELETON_MESSAGES["INITIAL_SEARCH"].format(seconds=search_estimation_seconds)
    fh_executor.schedule_send_message(
        message={
            "type": "flights_skeleton_async",
            "text": initial_search_message,
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    travel_context_dict: dict[str, Any] = json.loads(flight_search_criteria.get("travel_context") or "{}")

    serp_flight_options: list[Booking] | None = None
    serp_flight_search_params: dict[str, Any] | None = None
    choice: dict = {}
    airport_code = (
        flight_search_criteria["departure_airport_code"]
        if is_outbound_search
        else flight_search_criteria["arrival_airport_code"]
    )

    message_buffer_str = extral_params.get("message_strs") or []
    callback_handler = callback_handlers.get(step_name)

    duration_suggestion_string = None
    if is_outbound_search:
        search_source = __determine_search_source_for_outbound_search(
            current_working_segment_index,
            travel_context.flight_select_result.search_source,
            travel_context.flight_search_additional_criteria,
        )
        duration_suggestion_task = asyncio.create_task(
            _get_duration_suggestion(
                message_buffer_str=message_buffer_str, flight_search_criteria=flight_search_criteria
            )
        )

        if search_source == FlightSearchSource.FALLBACK_SPOTNANA:
            return await fallback_spotnana_search(
                fh_executor,
                flight_search_criteria,
                message_buffer_str,
                message_persistor,
                callback_handler,
                is_premium_cabin_search,
                airport_code,
                current_working_segment_index,
                flight_search_type,
                None,
                duration_suggestion_task,
            )

        # SERP + SPOTNANA parrallel search
        serp_search_task = None
        spotnana_search_task = None
        spotnana_raw_data = None

        message_text = ""
        try:
            serp_search_task = asyncio.create_task(
                fh_executor.serp_flight_search.search_flights_serp(
                    flight_search_criteria, fh_executor.schedule_send_message
                )
            )

            spotnana_search_task = asyncio.create_task(
                FlightSearchTools.search_flights_spotnana(
                    flight_search_criteria,
                    current_working_segment_index,
                    flight_search_type,
                    None,
                    None,  # spotnana search is in parrallel with SERP search, no need to send message
                )
            )

            try:
                choice, serp_flight_options = await serp_search_task
                duration_suggestion_string = await duration_suggestion_task
                serp_flight_search_params = fh_executor.serp_flight_search.flight_search_params

                if choice and serp_flight_options:
                    fh_executor.flight_choice_from_outbound_search = choice
                else:
                    asyncio.create_task(track_serp_not_found_metrics(fh_executor.user.id, fh_executor.thread.id))
                    logger.error("No outbound flights found in SERP", flight_mask)
                    message_text = FLIGHT_SKELETON_MESSAGES["SEARCH_FALLBACK"]
                    serp_flight_options = None
            except Exception as e:
                logger.error(f"SERP search failed: {str(e)}", flight_mask)
                message_text = FLIGHT_SKELETON_MESSAGES["SEARCH_FALLBACK"]
                serp_flight_options = None

            if not serp_flight_options:
                fh_executor.schedule_send_message(
                    message={
                        "type": "flights_skeleton_async",
                        "text": message_text,
                        "isBotMessage": True,
                        "expectResponse": False,
                    }
                )

                try:
                    logger.info("Falling back to search flights on Spotnana.", flight_mask)
                    asyncio.create_task(track_fallback_spotnana_metrics(fh_executor.user.id, fh_executor.thread.id))
                    spotnana_raw_data = await spotnana_search_task
                    if spotnana_raw_data:
                        spotnana_message_text = FLIGHT_SKELETON_MESSAGES["NARROWING_DOWN"].format(
                            count=len(spotnana_raw_data["flight_choices"])
                        )

                        fh_executor.schedule_send_message(
                            message={
                                "type": "flights_skeleton_async",
                                "text": spotnana_message_text,
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )

                    return await process_spotnana_search_results(
                        fh_executor,
                        spotnana_raw_data,
                        message_buffer_str,
                        travel_context_dict,
                        flight_search_criteria,
                        is_premium_cabin_search,
                        airport_code,
                        callback_handler,
                        message_persistor,
                        current_working_segment_index,
                        duration_suggestion_string,
                    )

                except Exception as e:
                    logger.error(f"Spotnana search failed: {str(e)}", flight_mask)
                    raise Exception("Spotnana search failed") from e

        except Exception as e:
            logger.error(f"Error during flight search: {str(e)}", flight_mask)
            empty_flight_message = AIMessage(content=FLIGHT_SKELETON_MESSAGES["NO_FLIGHTS"])
            await message_persistor([empty_flight_message])
            messages = await map_websocket_message(
                empty_flight_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**messages[0]})
            return empty_flight_message

    elif (
        flight_search_criteria.get("flight_type") == FlightType.RoundTrip
        or flight_search_criteria.get("flight_type") == FlightType.MultiLegs
    ):
        if (
            travel_context.flight_select_result.search_source is None
            or travel_context.flight_select_result.search_source == FlightSearchSource.SERP
        ) and current_working_segment_index is None:
            serp_flight_search_id = get_flight_search_id_for_return_search_from_serp_flight_id(
                flight_search_criteria.get("selected_outbound_flight_id")
            )
            assert serp_flight_search_id, "serp_flight_search_id is required for return flight search"
            _, serp_flight_options = await fh_executor.serp_flight_search.search_return_flights_serp(
                serp_flight_search_id,
                fh_executor.flight_choice_from_outbound_search or {},
            )
        else:
            return await fallback_spotnana_search(
                fh_executor,
                flight_search_criteria,
                message_buffer_str,
                message_persistor,
                callback_handler,
                is_premium_cabin_search,
                airport_code,
                current_working_segment_index,
                flight_search_type,
                travel_context.selected_outbound_flight.get_all_airline_codes()
                if travel_context.selected_outbound_flight
                else None,
                duration_suggestion_string,
            )

    if not serp_flight_options:
        asyncio.create_task(track_serp_not_found_metrics(fh_executor.user.id, fh_executor.thread.id))
        empty_flight_message = AIMessage(content=FLIGHT_SKELETON_MESSAGES["NO_FLIGHTS"])
        await message_persistor([empty_flight_message])
        messages = await map_websocket_message(
            empty_flight_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**messages[0]})
        return empty_flight_message

    if is_outbound_search:
        travel_context_dict.pop("return_departure_time", None)
        travel_context_dict.pop("return_arrival_time", None)
    else:
        travel_context_dict.pop("outbound_departure_time", None)
        travel_context_dict.pop("outbound_arrival_time", None)

    fh_executor.schedule_send_message(
        message={
            "type": "flights_skeleton_async",
            "text": FLIGHT_SKELETON_MESSAGES["NARROWING_DOWN"].format(count=len(serp_flight_options)),
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    function_message = await fh_executor.flight_helper.process_serp_flight_search_result(
        response=serp_flight_options,
        message_buffer_strs=[m for m in message_buffer_str if m.startswith("user")],
        travel_context_dict=travel_context_dict,
        is_return_search=flight_search_criteria.get("current_step") == "RETURN_FLIGHT_SEARCH",
        flight_search_type=flight_search_type,
        default_airline_codes=flight_search_criteria["default_airline_brands"] or [],
        preferred_airline_codes=flight_search_criteria["preferred_airline_codes"] or [],
        airport_code=flight_search_criteria["departure_airport_code"]
        if is_outbound_search
        else flight_search_criteria["arrival_airport_code"],
        is_premium_cabin_search=is_premium_cabin_search,
        serp_flight_search_params=serp_flight_search_params,
        duration_suggestion_string=duration_suggestion_string,
    )

    if isinstance(function_message, AgentError):
        return AIMessage(
            content="There was an error while searching for flights. Please try again later.",
        )
    else:
        function_message.additional_kwargs["step"] = (
            FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.value
            if is_outbound_search
            else FlightPlanningStep.RETURN_FLIGHT_SEARCH.value
        )
        await message_persistor([function_message])

        messages = await map_websocket_message(
            function_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**messages[0]})
        await _prompt_to_update_preferences(flight_search_criteria, fh_executor, message_persistor)
    return function_message


async def validate_flight_search_params(
    fh_executor: "fhe.TripPlanExecutor", flight_search_core: FlightSearchCoreCriteria
):
    validate_list = []
    if not flight_search_core.departure_airport_code:
        validate_list.append("departure airport code or city is missing")
    if not flight_search_core.arrival_airport_code:
        validate_list.append("arrival airport code or city is missing")
    if flight_search_core.departure_airport_code == flight_search_core.arrival_airport_code:
        validate_list.append(
            "looks like you're already in the destination given departure and arrival airport code or city are the same. is that right?"
        )

    if not flight_search_core.outbound_date:
        validate_list.append("departure date is missing")
    elif not is_valid_future_date(flight_search_core.outbound_date, fh_executor.timezone):
        validate_list.append(
            "A past date is provided for the outbound date. I won't be able to search for past flights."
        )

    if not flight_search_core.flight_type:
        validate_list.append("flight type is missing, is it one way or round trip?")

    if flight_search_core.flight_type == FlightType.RoundTrip:
        if not flight_search_core.return_date:
            validate_list.append("return date is missing for round trip")
        elif not is_valid_future_date(flight_search_core.return_date, fh_executor.timezone):
            validate_list.append(
                "A past date is provided for the return date. I won't be able to search for past flights."
            )
        elif not is_valid_future_date(
            flight_search_core.return_date, fh_executor.timezone, flight_search_core.outbound_date
        ):
            validate_list.append(
                "The return date is earlier than the outbound date. I can't search for round trip flight this way."
            )

    if validate_list:
        combined_response = await b.RephraseRequestForMissingOrInvalidInfo(
            sentences=[
                "Looks like there're something to clarify about your flight before I could proceed.",
                *validate_list,
            ],
            current_date=get_current_date_string(fh_executor.timezone),
            user_name=fh_executor.user.name,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            need_response_with_name=False,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
        return AIMessage(
            content=combined_response.combinedText,
            additional_kwargs={
                "agent_classification": AgentTypes.FLIGHTS,
            },
        )
    return None


async def fallback_spotnana_search(
    fh_executor: "fhe.TripPlanExecutor",
    flight_search_criteria: FlightSearchParams,
    message_buffer_str: list[str],
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    callback_handler: Callable | None,
    is_premium_cabin_search: bool,
    airport_code: str,
    current_segment_index: int | None,
    flight_search_type: FlightSearchType,
    selected_outbound_airline_codes: list[str] | None,
    duration_suggestion_task: asyncio.Task[str | None] | None,
):
    travel_context_dict: dict[str, Any] = json.loads(flight_search_criteria.get("travel_context") or "{}")
    logger.info(f"Searching flights on Spotnana with the following criteria: {flight_search_criteria}", flight_mask)

    spotnana_raw_data = await FlightSearchTools.search_flights_spotnana(
        flight_search_criteria,
        current_segment_index,
        flight_search_type,
        selected_outbound_airline_codes,
        fh_executor.schedule_send_message,
    )
    duration_suggestion_string = None
    if duration_suggestion_task:
        duration_suggestion_string = await duration_suggestion_task
    return await process_spotnana_search_results(
        fh_executor,
        spotnana_raw_data,
        message_buffer_str,
        travel_context_dict,
        flight_search_criteria,
        is_premium_cabin_search,
        airport_code,
        callback_handler,
        message_persistor,
        current_segment_index,
        duration_suggestion_string,
    )


async def process_spotnana_search_results(
    fh_executor: "fhe.TripPlanExecutor",
    spotnana_raw_data: dict[str, Any],
    message_buffer_str: list[str],
    travel_context_dict: dict[str, Any],
    flight_search_criteria: FlightSearchParams,
    is_premium_cabin_search: bool,
    airport_code: str,
    callback_handler: Callable | None,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    current_segment_index: int | None,
    duration_suggestion_string: str | None,
):
    function_message, spotnana_search_id, has_flights = await fh_executor.flight_helper.proccess_flight_search_results(
        flight_response=spotnana_raw_data,
        message_buffer_strs=message_buffer_str,
        travel_context_dict=travel_context_dict,
        is_return_search=flight_search_criteria["current_step"] == "RETURN_FLIGHT_SEARCH",
        is_premium_cabin_search=is_premium_cabin_search,
        airport_code=airport_code,
        default_airline_codes=flight_search_criteria["default_airline_brands"] or [],
        preferred_airline_codes=flight_search_criteria["preferred_airline_codes"] or [],
        current_segment_index=current_segment_index,
        duration_suggestion_string=duration_suggestion_string,
    )

    if isinstance(function_message, AgentError):
        return AIMessage(
            content="There was an error while searching for flights. Please try again later.",
        )
    else:
        function_message.additional_kwargs["step"] = (
            FlightPlanningStep.RETURN_FLIGHT_SEARCH.value
            if flight_search_criteria["current_step"] == "RETURN_FLIGHT_SEARCH"
            else FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.value
        )

        if callback_handler:
            callback_handler(spotnana_search_id, FlightSearchSource.FALLBACK_SPOTNANA)

        await message_persistor([function_message])

        messages = await map_websocket_message(
            function_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**messages[0]})
        if has_flights:
            await _prompt_to_update_preferences(flight_search_criteria, fh_executor, message_persistor)
        return function_message


async def _prompt_to_update_preferences(
    flight_search_criteria: FlightSearchParams,
    fh_executor: "fhe.TripPlanExecutor",
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
):
    response = await b.PromptUserToUpdatePreferencesAfterSearch(
        "flight", search_criteria=str(flight_search_criteria), preferences=None
    )
    if response:
        msg = AIMessage(
            content=response,
            additional_kwargs={"agent_classification": AgentTypes.PREFERENCES},
        )
        await message_persistor([msg])

        messages = await map_websocket_message(msg)

        await fh_executor.message_sender(message={**messages[0]})
